// truck-hydraulic-system-fields.js
// Field definitions and dynamic logic for the SYS-700CPK Configurator

import { evaluateMatrixValue } from './matrix-loader.js';
import { parsePartNumber } from './sys-700cpk.js';

// Helper caches for option lookups
const engineOptionsCache = new Map(),
      pumpOptionsCache = new Map(),
      kitOptionsCache = new Map();
const adapterOptionsCache = new Map();

// Helper for parsing pump options from XML
const parsePumpOptions = (xmlContent) => {
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlContent, "text/xml");
    const options = [];

    xmlDoc.querySelectorAll('Value').forEach(value => {
        const code = value.querySelector('Property[Name="Value"]')?.textContent;
        const description = value.querySelector('Property[Name="Description"]')?.textContent;
        const groups = Array.from(value.querySelectorAll('Group')).map(g => g.getAttribute('Name'));
        
        if (code && description) {
            options.push({ 
                value: code, 
                label: description,
                groups: groups 
            });
        }
    });

    return options;
}

// Field group definitions for UI organization
const fieldGroups = {
    truckSelections: {
        title: "Truck Selections",
        fields: ['Application', 'AGKitLessMiniPack', 'Make', 'Macfit', 'Year', 'Vehicle_Type', 'Engine', 'Pump', 'Kit', 'Clutch', 'Adapter']
    },
    agQuestions: {
        title: "AG Questions",
        fields: ['Product', 'Product_Part', 'Valve', 'Cab', 'Chassis', 'ACC_Hose', 'Aux_Hoses', 'Control', 'Harness'],
        condition: formState => formState.Application === 'AG'
    },
    commercialQuestions: {
        title: "Commercial Questions",
        fields: ['Product', 'Cab', 'Chassis', 'Control', 'Harness'],
        condition: formState => formState.Application === 'COM'
    }
};

// Field definitions
const fields = {
    Application: {
        label: "Application",
        type: "select",
        required: true,
        options: [
            { value: "AG", label: "Agriculture" },
            { value: "COM", label: "Commercial Application" },
            { value: "OTHER", label: "Other Application" },
            { value: "WRK", label: "Wrecker" }
        ]
    },

    AGKitLessMiniPack: {
        label: "Less AG Mini Pack?",
        type: "boolean",
        required: false,
        condition: function(formState) { return formState.Application === 'AG'; }
    },

    Make: {
        label: "Select Make of Vehicle",
        type: "select",
        required: true,
        condition: function(formState) { return formState.Application && formState.Application.length > 0; },
        async getOptions(formState) {
            console.log('Getting Make options for Application:', formState.Application);

            try {
                const response = await fetch('./data/Option_Lists/OptionList_Make.xml');
                if (!response.ok) throw new Error('Failed to load Make options');

                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");

                const makeOptions = [];
                xmlDoc.querySelectorAll('Value').forEach(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;
                    const groups = Array.from(value.querySelectorAll('Group')).map(g => g.getAttribute('Name'));

                    if (code && description) {
                        const shouldInclude = formState.Application === 'AG' ?
                            groups.includes('B') :
                            groups.includes('O');

                        if (shouldInclude) {
                            makeOptions.push({ value: code, label: description });
                        }
                    }
                });

                makeOptions.sort((a, b) => a.label.localeCompare(b.label));
                console.log('Make options loaded:', makeOptions);
                return makeOptions;
            } catch (error) {
                console.error('Error loading Make options:', error);
                // Fallback options
                const fallbackOptions = [
                    { value: "FD", label: "Ford" },
                    { value: "DG", label: "Dodge/Ram" },
                    { value: "GM", label: "Chevrolet/GMC" },
                    { value: "FL", label: "Freightliner" }
                ];

                return formState.Application === 'AG' ?
                    fallbackOptions.filter(opt => ['FD', 'DG', 'GM'].includes(opt.value)) :
                    fallbackOptions;
            }
        }
    },

    Macfit: {
        label: "Click info link select yes if kit will fit:",
        type: "boolean",
        required: true,
        condition: (formState) => formState.Make === 'MC',
        infoLink: "/bulletin/IB7042201Mack.pdf",
        critical: true,
        errorMessage: "Will Not Fit"
    },

    Vehicle_Type: {
        label: "Vehicle Type",
        type: "select",
        required: true,
        condition: function(formState) { return formState.Make; },
        async getOptions(formState) {
            try {
                const response = await fetch('./data/Option_Lists/OptionList_VehicleType.xml');
                if (!response.ok) throw new Error('Failed to load Vehicle Type options');

                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");

                const vehicleTypes = [];
                xmlDoc.querySelectorAll('Value').forEach(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;
                    const groups = Array.from(value.querySelectorAll('Group')).map(g => g.getAttribute('Name'));

                    if (code && description) {
                        const shouldInclude = formState.Application === 'AG' ?
                            groups.includes('B') :
                            groups.includes('C');

                        if (shouldInclude) {
                            vehicleTypes.push({ value: code, label: description });
                        }
                    }
                });

                return vehicleTypes;
            } catch (error) {
                console.error('Error loading Vehicle Type options:', error);
                return [
                    { value: "TRUCK", label: "Over The Road Truck" },
                    { value: "TRACTOR", label: "Farm Tractor" }
                ];
            }
        }
    },

    Year: {
        label: "Model Year",
        type: "select",
        required: true,
        condition: function(formState) { return formState.Vehicle_Type; },
        async getOptions(formState) {
            try {
                const response = await fetch('./data/Option_Lists/OptionList_Year.xml');
                if (!response.ok) throw new Error('Failed to load Year options');

                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");

                const yearOptions = [];
                xmlDoc.querySelectorAll('Value').forEach(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;
                    
                    if (code && description) {
                        yearOptions.push({ value: code, label: description });
                    }
                });

                // Sort years in descending order
                return yearOptions.sort((a, b) => parseInt(b.value) - parseInt(a.value));
            } catch (error) {
                console.error('Error loading Year options:', error);
                const currentYear = new Date().getFullYear();
                return Array.from({ length: 10 }, (_, i) => {
                    const year = currentYear - i;
                    return { value: year.toString(), label: year.toString() };
                });
            }
        }
    },

    Engine: {
        label: "Engine",
        type: "select",
        required: true,
        condition: function(formState) { return formState.Year && formState.Vehicle_Type; },
        async getOptions(formState) {
            const cacheKey = engineOptionsCache.getCacheKey(formState);
            const cached = engineOptionsCache.get(formState);
            if (cached) return cached;

            try {
                // First check matrix for engine options
                const matrixResult = await evaluateMatrixValue('Matrix_MultipleEngineGroups.xml', formState);
                if (matrixResult && matrixResult.length > 0) {
                    const engineGroups = matrixResult.split(',').map(g => g.trim());
                    
                    const response = await fetch('./data/Option_Lists/OptionList_Engine.xml');
                    if (!response.ok) throw new Error('Failed to load Engine options');

                    const text = await response.text();
                    const parser = new DOMParser();
                    const xmlDoc = parser.parseFromString(text, "text/xml");

                    const engineOptions = [];
                    xmlDoc.querySelectorAll('Value').forEach(value => {
                        const code = value.querySelector('Property[Name="Value"]')?.textContent;
                        const description = value.querySelector('Property[Name="Description"]')?.textContent;
                        const groups = Array.from(value.querySelectorAll('Group')).map(g => g.getAttribute('Name'));

                        if (code && description && engineGroups.some(group => groups.includes(group))) {
                            engineOptions.push({ value: code, label: description });
                        }
                    });

                    engineOptionsCache.set(formState, engineOptions);
                    return engineOptions;
                }
                return [];
            } catch (error) {
                console.error('Error loading Engine options:', error);
                return [];
            }
        }
    },

    Pump: {
        label: "Select Pump Size",
        type: "select",
        required: true,
        condition: (formState) => formState.Engine && formState.Engine !== 'NONE',
        async getOptions(formState) {
            // Skip matrix lookup if required fields aren't present
            if (!formState.Engine || !formState.Vehicle_Type || !formState.Year) {
                return [
                    { value: "9GL", label: "9 Gal Pump" },
                    { value: "12GL", label: "12 Gal Pump" }
                ];
            }

            // Use normalized values for matrix lookup
            const matrixVehicleType = normalizeVehicleType(formState.Vehicle_Type);
            const matrixAppType = normalizeApplicationType(formState.Application);

            try {
                // Get pump groups from matrix with normalized values
                const pumpGroups = await evaluateMatrixValue('PumpGroupMatrix', {
                    ...formState,
                    Vehicle_Type: matrixVehicleType,
                    Application: matrixAppType
                }, {
                    type: 'pump',
                    defaultValue: null
                });

                if (!pumpGroups) {
                    // Fallback pump options - using standard XGL format
                    return [
                        { value: "9GL", label: "9 Gal Pump" },
                        { value: "12GL", label: "12 Gal Pump" }
                    ];
                }

                // Try primary pump options list first
                const response = await fetch('./data/Option_Lists/OptionList_Pump_1T1.xml');
                const xmlText = await response.text();

                // If primary fails, try backup options
                if (!xmlText.includes('Value')) {
                    const altResponse = await fetch('./data/Option_Lists/OptionList_Pump.xml');
                    const altXmlText = await altResponse.text();
                    return parsePumpOptions(altXmlText, pumpGroups);
                }

                return parsePumpOptions(xmlText, pumpGroups);
            } catch (error) {
                console.error('Error loading pump options:', error);
                // Fallback with normalized values
                return [
                    { value: "9GL", label: "9 Gal Pump" },
                    { value: "12GL", label: "12 Gal Pump" }
                ];
            }
        }
    },

    Kit: {
        label: "Select Your Kit",
        type: "select",
        required: true,
        condition: (formState) => formState.Engine && formState.Year && formState.Pump && formState.Vehicle_Type && formState.Application,
        async getOptions(formState) {
            console.debug('Getting Kit options for:', {
                Engine: formState.Engine,
                Year: formState.Year,
                Pump: formState.Pump,
                Vehicle_Type: formState.Vehicle_Type,
                Application: formState.Application,
                normalized: {
                    Engine: formState.Engine,
                    Year: formState.Year,
                    Pump: formState.Pump,
                    Vehicle_Type: normalizeVehicleType(formState.Vehicle_Type),
                    App: formState.Application === 'AG' ? 'B' : 'C'
                }
            });

            // Normalize values for matrix lookup
            const engineCode = formState.Engine;  // e.g., FD67D
            const normalizedPump = formState.Pump?.replace(/HP$/, '');  // Remove HP suffix if present

            console.debug('Normalized values for Kit lookup:', {
                original: {
                    Engine: formState.Engine,
                    Year: formState.Year,
                    Pump: formState.Pump,
                    Vehicle_Type: formState.Vehicle_Type,
                    Application: formState.Application
                },
                normalized: {
                    Engine: engineCode,
                    Year: formState.Year,
                    Pump: normalizedPump,
                    Vehicle_Type: 'OT',  // Always use OT for kit lookups
                    App: formState.Application === 'AG' ? 'A' : 'C'  // AG -> A, COM/OTHER -> C
                }
            });

            // Get kit groups from Kit_Group matrix using normalized values
            let kitGroups = await evaluateMatrixValue('Kit_Group', {
                Engine: engineCode,
                Year: formState.Year,
                Pump: normalizedPump,
                Vehicle_Type: 'OT',  // Always use OT for kit lookups
                App: formState.Application === 'AG' ? 'B' : 'C'  // AG -> B, COM/OTHER -> C
            }, {
                defaultValue: null,
                debug: true,
                fallbackValue: '620'  // Default kit group for new configurations
            });

            // If no match found, try without pump size
            if (!kitGroups || kitGroups === 'NA') {
                console.debug('Trying generic pump fallback...');
                kitGroups = await evaluateMatrixValue('Kit_Group', {
                    Engine: engineCode,
                    Year: formState.Year,
                    Pump: '*',  // Match any pump size
                    Vehicle_Type: 'OT',
                    App: formState.Application === 'AG' ? 'A' : 'C'
                }, {
                    defaultValue: null,
                    debug: true
                });
            }

            // If still no match, try previous year as fallback
            if (!kitGroups || kitGroups === 'NA') {
                const prevYear = (parseInt(formState.Year) - 1).toString();
                console.debug('Trying previous year fallback:', prevYear);
                kitGroups = await evaluateMatrixValue('Kit_Group', {
                    Engine: engineCode,
                    Year: prevYear,
                    Pump: normalizedPump,
                    Vehicle_Type: 'OT',
                    App: formState.Application === 'AG' ? 'A' : 'C'
                }, {
                    defaultValue: null,
                    debug: true
                });
            }

            console.debug('Kit groups result:', {
                groups: kitGroups,
                isNull: kitGroups === null,
                isNA: kitGroups === 'NA',
                type: typeof kitGroups
            });

            if (!kitGroups || kitGroups === 'NA') {
                console.warn('No kit options available - Matrix returned:', kitGroups);
                return [];
            }

            // Load Kit option list
            try {
                const response = await fetch('./data/Option_Lists/OptionList_Kit.xml');
                if (!response.ok) throw new Error('Failed to load Kit options');

                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");

                const kitOptions = [];
                const values = xmlDoc.querySelectorAll('Value');

                values.forEach(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;
                    const groups = Array.from(value.querySelectorAll('Group')).map(g => g.getAttribute('Name'));

                    if (code && description) {
                        // Ensure we have arrays to work with
                        const groupsToCheck = Array.isArray(kitGroups) ? kitGroups : [kitGroups];

                        // Log raw values before normalization
                        console.debug('Raw values before normalization:', {
                            kitGroup: kitGroups,
                            xmlGroups: groups,
                            groupsToCheck
                        });

                        // Extract and normalize each kit group
                        const normalizedKitGroups = groupsToCheck.map(g =>
                            padGroup(cleanGroup(g.toString()))
                        );

                        // Normalize the XML groups
                        const normalizedItemGroups = groups
                            .filter(g => g != null)
                            .map(g => padGroup(cleanGroup(g)));

                        // Check if any normalized kit group matches any of the normalized item groups
                        const shouldInclude = normalizedKitGroups.some(kg =>
                            normalizedItemGroups.some(ig => kg === ig)
                        );

                        console.debug('Kit option evaluation:', {
                            code,
                            shouldInclude
                        });

                        if (shouldInclude) {
                            kitOptions.push({ value: code, label: code }); // Use code as label for kits
                        }
                    }
                });

                console.log('Kit options loaded:', kitOptions);
                return kitOptions;
            } catch (error) {
                console.error('Error loading Kit options:', error);
                // Fallback kit options based on video scenarios
                return [
                    { value: "700536", label: "700536" },
                    { value: "700607", label: "700607" }
                ];
            }
        }
    },

    Clutch: {
        label: "Select Your Clutch",
        type: "select",
        required: true,
        condition: (formState) => formState.Kit && formState.Kit.length > 0,
        async getOptions(formState) {
            console.log('Getting Clutch options for Kit:', formState.Kit);

            // Get clutch groups from matrices
            const clutchFilterPre = await evaluateMatrixValue('Clutch_Group', formState);
            const clutchExceptions = await evaluateMatrixValue('HD_Clutch', formState);
            const clutchFilter = clutchExceptions || clutchFilterPre;

            console.log('Clutch filter result:', clutchFilter);

            // Load Clutch option list
            try {
                const response = await fetch('./data/Option_Lists/OptionList_Clutch.xml');
                if (!response.ok) throw new Error('Failed to load Clutch options');

                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");

                const clutchOptions = [];
                const values = xmlDoc.querySelectorAll('Value');

                values.forEach(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;
                    const groups = Array.from(value.querySelectorAll('Group')).map(g => g.getAttribute('Name'));

                    if (code && description) {
                        const groupsToCheck = Array.isArray(clutchFilter) ? clutchFilter : [clutchFilter];
                        const shouldInclude = !clutchFilter || groupsToCheck.some(group => groups.includes(group));

                        if (shouldInclude) {
                            clutchOptions.push({ value: code, label: description });
                        }
                    }
                });

                console.log('Clutch options loaded:', clutchOptions);
                return clutchOptions;
            } catch (error) {
                console.error('Error loading Clutch options:', error);
                // Fallback clutch options from video scenarios
                return [
                    { value: "8GRVA", label: "8 Groove Ogura \"A\"" },
                    { value: "6GRVA", label: "6 Groove Ogura \"A\"" }
                ];
            }
    },

    // AG-specific fields that appear in the "AG Questions" section
    Product: {
        label: "Select DewEze Product",
        type: "select",
        required: true,
        condition: (formState) => formState.Application === 'AG',
        async getOptions(formState) {
            try {
                const response = await fetch('./data/Option_Lists/OptionList_Product.xml');
                if (!response.ok) throw new Error('Failed to load Product options');

                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");

                const productOptions = [];
                const values = xmlDoc.querySelectorAll('Value');

                values.forEach(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;

                    if (code && description) {
                        productOptions.push({ value: code, label: description });
                    }
                });

                return productOptions;
            } catch (error) {
                console.error('Error loading Product options:', error);
                // Fallback from video scenarios
                return [
                    { value: "DB800", label: "Deweze Balebed 800-900 Series" },
                    { value: "RT800", label: "RT/XRT 800-900 Bed" }
                ];
            }
        }
    },

    Valve: {
        label: "What Type of valve do you want",
        type: "select",
        required: true,
        condition: (formState) => formState.Application === 'AG' && formState.Product && formState.Product !== '660',
        async getOptions(formState) {
            // Get valve groups from MultipleValveGroups matrix
            const valveGroups = await evaluateMatrixValue('MultipleValveGroups', formState);

            try {
                const response = await fetch('./data/Option_Lists/OptionList_Valve.xml');
                if (!response.ok) throw new Error('Failed to load Valve options');

                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");

                const valveOptions = [];
                const values = xmlDoc.querySelectorAll('Value');

                values.forEach(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;
                    const groups = Array.from(value.querySelectorAll('Group')).map(g => g.getAttribute('Name'));

                    if (code && description) {
                        const groupsToCheck = Array.isArray(valveGroups) ? valveGroups : [valveGroups];
                        const shouldInclude = !valveGroups || groupsToCheck.some(group => groups.includes(group));

                        if (shouldInclude) {
                            valveOptions.push({ value: code, label: description });
                        }
                    }
                });

                return valveOptions;
            } catch (error) {
                console.error('Error loading Valve options:', error);
                // Fallback from video scenarios
                return [
                    { value: "NONPROP", label: "Non-Proportional" },
                    { value: "3HFDB", label: "3 Bank Hydra Force Double Poppet" }
                ];
            }
        }
    },

    Cab: {
        label: "Select Cab Type",
        type: "select",
        required: true,
        condition: (formState) => formState.Application === 'AG',
        async getOptions(formState) {
            try {
                const response = await fetch('./data/Option_Lists/OptionList_Cab.xml');
                if (!response.ok) throw new Error('Failed to load Cab options');

                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");

                const cabOptions = [];
                const values = xmlDoc.querySelectorAll('Value');

                values.forEach(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;

                    if (code && description) {
                        cabOptions.push({ value: code, label: description });
                    }
                });

                return cabOptions;
            } catch (error) {
                console.error('Error loading Cab options:', error);
                // Fallback from video scenarios
                return [
                    { value: "CREW", label: "Crew Cab" },
                    { value: "REG", label: "Regular Cab" },
                    { value: "EXT", label: "Extended Cab" }
                ];
            }
        }
    },

    Chassis: {
        label: "Select Chassis Type",
        type: "select",
        required: true,
        condition: (formState) => formState.Application === 'AG',
        async getOptions(formState) {
            try {
                const response = await fetch('./data/Option_Lists/OptionList_Chassis.xml');
                if (!response.ok) throw new Error('Failed to load Chassis options');

                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");

                const chassisOptions = [];
                const values = xmlDoc.querySelectorAll('Value');

                values.forEach(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;

                    if (code && description) {
                        chassisOptions.push({ value: code, label: description });
                    }
                });

                return chassisOptions;
            } catch (error) {
                console.error('Error loading Chassis options:', error);
                // Fallback from video scenarios
                return [
                    { value: "BOX", label: "Box Take-Off" }
                ];
            }
        }
    },

    ACC_Hose: {
        label: "Do you want auxiliary hoses?",
        type: "boolean",
        required: true,
        condition: (formState) => formState.Application === 'AG' && formState.Valve &&
            ['3HF', '3HFDB', '3BCL', '4BCL'].includes(formState.Valve)
    },

    Aux_Hoses: {
        label: "Choose Auxiliary Hoses",
        type: "select",
        required: true,
        condition: (formState) => formState.ACC_Hose === true,
        async getOptions(formState) {
            try {
                const response = await fetch('./data/Option_Lists/OptionList_Aux_Hoses.xml');
                if (!response.ok) throw new Error('Failed to load Aux_Hoses options');

                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");

                const auxOptions = [];
                const values = xmlDoc.querySelectorAll('Value');

                values.forEach(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;

                    if (code && description) {
                        auxOptions.push({ value: code, label: description });
                    }
                });

                return auxOptions;
            } catch (error) {
                console.error('Error loading Aux_Hoses options:', error);
                return [
                    { value: "AUX1", label: "Standard Auxiliary Hoses" }
                ];
            }
        }
    },

    Control: {
        label: "Select Control Option",
        type: "select",
        required: true,
        condition: (formState) => formState.Application === 'AG',
        async getOptions(formState) {
            try {
                const response = await fetch('./data/Option_Lists/OptionList_Control.xml');
                if (!response.ok) throw new Error('Failed to load Control options');

                const text = await response.text();
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(text, "text/xml");

                const controlOptions = [];
                const values = xmlDoc.querySelectorAll('Value');

                values.forEach(value => {
                    const code = value.querySelector('Property[Name="Value"]')?.textContent;
                    const description = value.querySelector('Property[Name="Description"]')?.textContent;
                    const groups = Array.from(value.querySelectorAll('Group')).map(g => g.getAttribute('Name'));

                    if (code && description) {
                        // Filter based on product type (660 vs others)
                        const group = formState.Product === '660' ? '1' : '0';
                        const shouldInclude = groups.includes(group);

                        if (shouldInclude) {
                            controlOptions.push({ value: code, label: description });
                        }
                    }
                });

                return controlOptions;
            } catch (error) {
                console.error('Error loading Control options:', error);
                // Fallback from video scenarios
                return [
                    { value: "WIRELESS", label: "Wireless Remote" }
                ];
            }
        }
    },

    Harness: {
        label: "Select Your Adapter",
        type: "select",
        required: true,
        condition: (formState) => formState.Application === 'AG',
        async getOptions(formState) {
            // Skip matrix lookup if required fields aren't present
            if (!formState.Make || !formState.Year) {
                console.debug('Skipping harness matrix lookup - missing required fields');
                return [{ value: "Standard Harness", label: "Standard Harness" }];
            }

            // Get harness from HarnessMatrix - normalize Make to match matrix values
            let normalizedMake = formState.Make;
            if (formState.Make === 'Ford') normalizedMake = 'FD';
            else if (formState.Make === 'Dodge') normalizedMake = 'DG';
            else if (formState.Make === 'Chevrolet') normalizedMake = 'CH';

            console.debug('Looking up harness with:', {
                Year: formState.Year,
                Make: normalizedMake,
                Chassis: formState.Chassis || 'Unknown'
            });

            const harnessValue = await evaluateMatrixValue('HarnessMatrix', {
                'Year =': formState.Year,
                'Make =': normalizedMake,
                'Chassis =': formState.Chassis || ''
            }, { defaultValue: null });

            if (harnessValue) {
                return [{ value: harnessValue, label: harnessValue }];
            }

            // Fallback based on make and year
            const harnessMap = {
                'Ford': "Harness, FD Box Delete 2023+",
                'DG': "Harness Adapter, Dodge Box Delete 2019+"
            };

            const defaultHarness = harnessMap[formState.Make] || "Standard Harness";
            return [{ value: defaultHarness, label: defaultHarness }];
        }
    }
};

// Helper functions
function isFieldVisible(fieldName, formState) {
    const field = fields[fieldName];
    if (!field) return false;
    if (field.condition) return field.condition(formState);
    return true;
}

// Export the public API
const moduleExports = {
    fields,
    fieldGroups,
    isFieldVisible,
    parsePumpOptions
};

export default moduleExports;
