// Authentication system for Harper Configurator
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.sessionKey = 'harper_auth_session';
        this.init();
    }

    init() {
        // Check for existing session on page load
        this.checkSession();
        
        // If we're on the login page, don't redirect
        if (window.location.pathname.includes('login.html')) {
            return;
        }
        
        // If not authenticated and not on login page, redirect to login
        if (!this.isAuthenticated() && !window.location.pathname.includes('login.html')) {
            this.redirectToLogin();
        } else if (this.isAuthenticated()) {
            this.showUserInfo();
        }
    }

    checkSession() {
        const sessionData = localStorage.getItem(this.sessionKey);
        if (sessionData) {
            try {
                const session = JSON.parse(sessionData);
                // Check if session is still valid (24 hours)
                const now = new Date().getTime();
                if (now - session.timestamp < 24 * 60 * 60 * 1000) {
                    this.currentUser = session.user;
                    return true;
                }
            } catch (e) {
                console.error('Invalid session data:', e);
            }
        }
        this.clearSession();
        return false;
    }

    login(username, password) {
        // Simple demo authentication - in production, this would call your backend
        const validUsers = {
            'admin': 'password',
            'user1': 'pass123',
            'demo': 'demo'
        };

        if (validUsers[username] && validUsers[username] === password) {
            const user = {
                username: username,
                name: username === 'admin' ? 'Administrator' : 
                      username === 'user1' ? 'John Doe' : 'Demo User',
                role: username === 'admin' ? 'admin' : 'user',
                preferences: {
                    theme: 'light',
                    defaultConfigurator: 'sys-700cpk'
                }
            };

            this.currentUser = user;
            this.saveSession(user);
            return { success: true, user: user };
        }

        return { success: false, error: 'Invalid username or password' };
    }

    logout() {
        this.currentUser = null;
        this.clearSession();
        this.redirectToLogin();
    }

    saveSession(user) {
        const sessionData = {
            user: user,
            timestamp: new Date().getTime()
        };
        localStorage.setItem(this.sessionKey, JSON.stringify(sessionData));
    }

    clearSession() {
        localStorage.removeItem(this.sessionKey);
    }

    isAuthenticated() {
        return this.currentUser !== null;
    }

    getCurrentUser() {
        return this.currentUser;
    }

    redirectToLogin() {
        // Store the current page to redirect back after login
        if (!window.location.pathname.includes('login.html')) {
            localStorage.setItem('harper_redirect_after_login', window.location.href);
        }
        window.location.href = 'login.html';
    }

    redirectToDashboard() {
        // Check if there's a stored redirect URL
        const redirectUrl = localStorage.getItem('harper_redirect_after_login');
        if (redirectUrl) {
            localStorage.removeItem('harper_redirect_after_login');
            window.location.href = redirectUrl;
        } else {
            window.location.href = 'index.html';
        }
    }

    showUserInfo() {
        const authCheck = document.getElementById('auth-check');
        const userName = document.getElementById('user-name');
        
        if (authCheck && userName && this.currentUser) {
            userName.textContent = this.currentUser.name;
            authCheck.style.display = 'block';
        }
    }
}

// Global auth manager instance
const authManager = new AuthManager();

// Global logout function
function logout() {
    authManager.logout();
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthManager;
}
