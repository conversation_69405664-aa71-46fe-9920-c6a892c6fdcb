document.addEventListener('DOMContentLoaded', function () {
    const form = document.getElementById('demo-form');
    const application = document.getElementById('application');
    const agQuestions = document.getElementById('agQuestions');
    const agMiniPackGroup = document.getElementById('agMiniPackGroup');
    const vehicleMake = document.getElementById('vehicleMake');
    const engine = document.getElementById('engine');
    const adapter = document.getElementById('adapter');

    // Engine options based on vehicle make
    const engineOptions = {
        ford: ['Ford 6.7L Diesel'],
        freightliner: ['Freightliner M2 Cummins'],
        dodge: ['Dodge/Ram 6.7L Diesel']
    };

    // Adapter configurations based on the video transcripts
    const adapterConfigs = {
        ford_ag: '03-51261; S=16HB-16MB90; P=10MJ-12MB45',
        freightliner_commercial: '03-6923; S=20MB90; P=12MJ-12MB90',
        dodge_ag: '03-1920; S=16HB-16MB90; P=10MJ-12MB90'
    };

    // Show/hide AG specific questions based on application
    application.addEventListener('change', function () {
        agQuestions.style.display = this.value === 'agriculture' ? 'block' : 'none';
        agMiniPackGroup.style.display = this.value === 'agriculture' ? 'block' : 'none';

        // Clear adapter when application changes
        adapter.value = '';
    });

    // Update engine options when vehicle make changes
    vehicleMake.addEventListener('change', function () {
        const make = this.value;
        engine.innerHTML = '<option value="">Select Engine</option>';

        if (engineOptions[make]) {
            engineOptions[make].forEach(engineOption => {
                const option = document.createElement('option');
                option.value = engineOption.toLowerCase().replace(/\s+/g, '-');
                option.textContent = engineOption;
                engine.appendChild(option);
            });
        }

        // Clear adapter when make changes
        adapter.value = '';
    });

    // Form submission handler
    form.addEventListener('submit', function (e) {
        e.preventDefault();

        const formData = new FormData(form);
        const appType = formData.get('application');
        const make = formData.get('vehicleMake');

        // Set the appropriate adapter based on configuration
        if (appType === 'agriculture' && make === 'ford') {
            adapter.value = adapterConfigs.ford_ag;
        } else if (appType === 'commercial' && make === 'freightliner') {
            adapter.value = adapterConfigs.freightliner_commercial;
        } else if (appType === 'agriculture' && make === 'dodge') {
            adapter.value = adapterConfigs.dodge_ag;
        }

        // You would typically send this data to a server here
        console.log('Form submitted:', Object.fromEntries(formData));
    });
});
