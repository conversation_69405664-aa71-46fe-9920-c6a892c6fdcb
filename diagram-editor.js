// Mermaid Diagram Editor with Resizable Split Pane
class DiagramEditor {
    constructor() {
        this.editor = null;
        this.isFullscreen = false;
        this.isResizing = false;
        this.startX = 0;
        this.startY = 0;
        this.startWidth = 0;
        this.startHeight = 0;
        this.isMobile = window.innerWidth <= 768;
        
        this.defaultCode = `graph TD
    A[Start] --> B{Is it working?}
    B -->|Yes| C[Great!]
    B -->|No| D[Debug]
    D --> B
    C --> E[End]`;
        
        this.init();
    }
    
    async init() {
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            fontFamily: 'Arial, sans-serif'
        });
        
        // Initialize Monaco Editor
        await this.initMonacoEditor();
        
        // Initialize resizer
        this.initResizer();
        
        // Initial render
        this.updatePreview();
        
        // Handle window resize
        window.addEventListener('resize', () => {
            this.handleWindowResize();
        });
    }
    
    async initMonacoEditor() {
        return new Promise((resolve) => {
            require.config({ paths: { vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.44.0/min/vs' } });
            require(['vs/editor/editor.main'], () => {
                this.editor = monaco.editor.create(document.getElementById('monaco-editor'), {
                    value: this.defaultCode,
                    language: 'markdown',
                    theme: 'vs-dark',
                    automaticLayout: true,
                    minimap: { enabled: false },
                    scrollBeyondLastLine: false,
                    fontSize: 14,
                    lineNumbers: 'on',
                    wordWrap: 'on'
                });
                
                // Listen for content changes
                this.editor.onDidChangeModelContent(() => {
                    this.debounceUpdatePreview();
                });
                
                resolve();
            });
        });
    }
    
    initResizer() {
        const resizer = document.getElementById('resizer');
        const container = document.getElementById('editor-container');
        
        resizer.addEventListener('mousedown', (e) => {
            this.startResize(e);
        });
        
        document.addEventListener('mousemove', (e) => {
            if (this.isResizing) {
                this.doResize(e);
            }
        });
        
        document.addEventListener('mouseup', () => {
            this.stopResize();
        });
        
        // Touch events for mobile
        resizer.addEventListener('touchstart', (e) => {
            this.startResize(e.touches[0]);
        });
        
        document.addEventListener('touchmove', (e) => {
            if (this.isResizing) {
                e.preventDefault();
                this.doResize(e.touches[0]);
            }
        });
        
        document.addEventListener('touchend', () => {
            this.stopResize();
        });
    }
    
    startResize(e) {
        this.isResizing = true;
        this.startX = e.clientX;
        this.startY = e.clientY;
        
        const container = document.getElementById('editor-container');
        const editorPane = container.querySelector('.editor-pane');
        const previewPane = container.querySelector('.preview-pane');
        
        if (this.isMobile) {
            this.startHeight = editorPane.offsetHeight;
        } else {
            this.startWidth = editorPane.offsetWidth;
        }
        
        document.getElementById('resizer').classList.add('dragging');
        document.body.style.cursor = this.isMobile ? 'row-resize' : 'col-resize';
        document.body.style.userSelect = 'none';
    }
    
    doResize(e) {
        if (!this.isResizing) return;
        
        const container = document.getElementById('editor-container');
        const editorPane = container.querySelector('.editor-pane');
        const previewPane = container.querySelector('.preview-pane');
        
        if (this.isMobile) {
            const deltaY = e.clientY - this.startY;
            const newHeight = this.startHeight + deltaY;
            const containerHeight = container.offsetHeight;
            const minHeight = 150;
            const maxHeight = containerHeight - minHeight;
            
            if (newHeight >= minHeight && newHeight <= maxHeight) {
                editorPane.style.height = newHeight + 'px';
                previewPane.style.height = (containerHeight - newHeight - 4) + 'px';
            }
        } else {
            const deltaX = e.clientX - this.startX;
            const newWidth = this.startWidth + deltaX;
            const containerWidth = container.offsetWidth;
            const minWidth = 250;
            const maxWidth = containerWidth - minWidth;
            
            if (newWidth >= minWidth && newWidth <= maxWidth) {
                editorPane.style.width = newWidth + 'px';
                previewPane.style.width = (containerWidth - newWidth - 4) + 'px';
            }
        }
        
        // Trigger Monaco editor resize
        if (this.editor) {
            this.editor.layout();
        }
    }
    
    stopResize() {
        this.isResizing = false;
        document.getElementById('resizer').classList.remove('dragging');
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
    }
    
    handleWindowResize() {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth <= 768;
        
        if (wasMobile !== this.isMobile) {
            // Reset pane sizes when switching between mobile and desktop
            const editorPane = document.querySelector('.editor-pane');
            const previewPane = document.querySelector('.preview-pane');
            editorPane.style.width = '';
            editorPane.style.height = '';
            previewPane.style.width = '';
            previewPane.style.height = '';
        }
        
        if (this.editor) {
            this.editor.layout();
        }
    }
    
    debounceUpdatePreview() {
        clearTimeout(this.updateTimeout);
        this.updateTimeout = setTimeout(() => {
            this.updatePreview();
        }, 300);
    }
    
    async updatePreview() {
        const code = this.editor.getValue();
        const previewElement = document.getElementById('mermaid-preview');
        
        try {
            // Clear previous content
            previewElement.innerHTML = '';
            
            // Create a unique ID for this diagram
            const diagramId = 'mermaid-diagram-' + Date.now();
            
            // Validate and render the diagram
            const { svg } = await mermaid.render(diagramId, code);
            previewElement.innerHTML = svg;
            
        } catch (error) {
            console.error('Mermaid rendering error:', error);
            previewElement.innerHTML = `
                <div class="error-message">
                    <strong>Syntax Error:</strong><br>
                    ${error.message || 'Invalid Mermaid syntax'}
                </div>
            `;
        }
    }
}

// Global functions
function toggleFullscreen() {
    const container = document.getElementById('editor-container');
    const editor = window.diagramEditor;
    
    if (!editor.isFullscreen) {
        container.classList.add('fullscreen');
        editor.isFullscreen = true;
    } else {
        container.classList.remove('fullscreen');
        editor.isFullscreen = false;
    }
    
    // Trigger layout update
    setTimeout(() => {
        if (editor.editor) {
            editor.editor.layout();
        }
    }, 100);
}

function exportDiagram() {
    const svgElement = document.querySelector('#mermaid-preview svg');
    if (!svgElement) {
        alert('No diagram to export. Please create a valid Mermaid diagram first.');
        return;
    }
    
    // Create a blob with the SVG content
    const svgData = new XMLSerializer().serializeToString(svgElement);
    const blob = new Blob([svgData], { type: 'image/svg+xml' });
    
    // Create download link
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'mermaid-diagram.svg';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

function goBack() {
    window.location.href = 'index.html';
}

// Initialize the editor when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.diagramEditor = new DiagramEditor();
});
