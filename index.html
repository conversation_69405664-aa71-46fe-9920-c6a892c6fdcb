<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Harper Configurator Prototypes</title>
  <link rel="stylesheet" href="sys-700cpk.css">
  <style>
    main { max-width: 500px; margin: 3rem auto; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px #0001; padding: 2rem; text-align: center; }
    h1 { color: #2a3a4a; }
    .links { display: flex; flex-direction: column; gap: 1.5rem; margin-top: 2rem; }
    a { display: block; background: #2a7ae2; color: #fff; text-decoration: none; padding: 1rem; border-radius: 4px; font-size: 1.2rem; transition: background 0.2s; }
    a:hover { background: #1a5bb8; }
    .user-info { background: #f8f9fa; padding: 1rem; border-radius: 6px; margin-bottom: 2rem; }
    .logout-btn { background: #dc3545; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer; }
    .logout-btn:hover { background: #c82333; }
  </style>
</head>
<body>
  <main>
    <div id="auth-check" style="display: none;">
      <div class="user-info">
        <p>Welcome back, <span id="user-name"></span>!</p>
        <button class="logout-btn" onclick="logout()">Logout</button>
      </div>
    </div>
    <h1>Harper Configurator Prototypes</h1>
    <p>Select a configurator to begin:</p>
    <div class="links">
      <a href="sys-700cpk.html">Truck Hydraulic System Configurator</a>
      <a href="sys-butler.html">Butler Manufacturing Configurator</a>
      <a href="diagram-editor.html">Mermaid Diagram Editor</a>
    </div>
  </main>
  <script src="auth.js"></script>
</body>
</html>
