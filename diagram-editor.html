<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mermaid Diagram Editor - Harper Configurator</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #1e1e1e;
      color: #fff;
      overflow: hidden;
      height: 100vh;
    }
    
    .header {
      background: #2d2d30;
      padding: 0.75rem 1rem;
      border-bottom: 1px solid #3e3e42;
      display: flex;
      justify-content: space-between;
      align-items: center;
      z-index: 1000;
      position: relative;
    }
    
    .header h1 {
      font-size: 1.2rem;
      font-weight: 500;
    }
    
    .header-actions {
      display: flex;
      gap: 0.5rem;
      align-items: center;
      flex-wrap: wrap;
    }

    @media (max-width: 600px) {
      .header-actions {
        gap: 0.25rem;
      }

      .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
      }
    }
    
    .btn {
      background: #0e639c;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.875rem;
      transition: background-color 0.2s;
    }
    
    .btn:hover {
      background: #1177bb;
    }
    
    .btn-secondary {
      background: #5a5a5a;
    }
    
    .btn-secondary:hover {
      background: #6a6a6a;
    }
    
    .editor-container {
      display: flex;
      height: calc(100vh - 60px);
      position: relative;
    }
    
    .editor-pane {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-width: 300px;
    }
    
    .preview-pane {
      flex: 1;
      display: flex;
      flex-direction: column;
      background: #fff;
      min-width: 300px;
    }
    
    .pane-header {
      background: #2d2d30;
      padding: 0.5rem 1rem;
      border-bottom: 1px solid #3e3e42;
      font-size: 0.875rem;
      font-weight: 500;
    }
    
    .preview-pane .pane-header {
      background: #f3f3f3;
      color: #333;
      border-bottom: 1px solid #ddd;
    }
    
    #monaco-editor {
      flex: 1;
      width: 100%;
    }
    
    #mermaid-preview {
      flex: 1;
      padding: 2rem;
      overflow: auto;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #fff;
      color: #333;
    }
    
    .resizer {
      width: 6px;
      background: #3e3e42;
      cursor: col-resize;
      position: relative;
      transition: all 0.2s;
      flex-shrink: 0;
      z-index: 100;
      border-left: 1px solid #2a2a2a;
      border-right: 1px solid #2a2a2a;
    }

    .resizer:hover {
      background: #007acc;
      width: 8px;
      box-shadow: 0 0 10px rgba(0, 122, 204, 0.3);
    }

    .resizer.dragging {
      background: #007acc;
      width: 8px;
      box-shadow: 0 0 10px rgba(0, 122, 204, 0.5);
    }

    body.fullscreen .resizer {
      background: #4a4a4a;
      border-color: #333;
    }

    body.fullscreen .resizer:hover,
    body.fullscreen .resizer.dragging {
      background: #007acc;
      box-shadow: 0 0 10px rgba(0, 122, 204, 0.5);
    }

    /* Add a visible grip pattern */
    .resizer::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 3px;
      height: 30px;
      background: repeating-linear-gradient(
        to bottom,
        rgba(255,255,255,0.3) 0px,
        rgba(255,255,255,0.3) 2px,
        transparent 2px,
        transparent 4px
      );
      border-radius: 1px;
    }

    .resizer:hover::before {
      background: repeating-linear-gradient(
        to bottom,
        rgba(255,255,255,0.6) 0px,
        rgba(255,255,255,0.6) 2px,
        transparent 2px,
        transparent 4px
      );
    }

    /* Add dots for better visibility */
    .resizer::after {
      content: '⋮';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: rgba(255,255,255,0.4);
      font-size: 16px;
      line-height: 1;
      pointer-events: none;
    }

    .resizer:hover::after {
      color: rgba(255,255,255,0.8);
    }
    
    .error-message {
      color: #f48771;
      padding: 1rem;
      background: #2d1b1b;
      border-left: 3px solid #f48771;
      margin: 1rem;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      font-size: 0.875rem;
    }
    
    /* Mobile responsive */
    @media (max-width: 768px) {
      .editor-container {
        flex-direction: column;
      }

      .resizer {
        width: 100%;
        height: 6px;
        cursor: row-resize;
      }

      .resizer:hover {
        height: 8px;
      }

      .resizer::before {
        width: 30px;
        height: 3px;
        background: repeating-linear-gradient(
          to right,
          rgba(255,255,255,0.3) 0px,
          rgba(255,255,255,0.3) 2px,
          transparent 2px,
          transparent 4px
        );
      }

      .resizer::after {
        content: '⋯';
        font-size: 14px;
      }

      .editor-pane,
      .preview-pane {
        min-width: unset;
        min-height: 200px;
      }
    }
    
    body.fullscreen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      z-index: 9999;
      background: #1e1e1e;
      overflow: hidden;
    }

    body.fullscreen .header {
      position: relative;
      z-index: 10000;
      background: #2d2d30;
    }

    body.fullscreen .editor-container {
      height: calc(100vh - 60px);
    }
    
    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #666;
      font-style: italic;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>Mermaid Diagram Editor</h1>
    <div class="header-actions">
      <button class="btn btn-secondary" onclick="toggleFullscreen()">Toggle Fullscreen</button>
      <button class="btn" onclick="exportDiagram()">Export SVG</button>
      <button class="btn btn-secondary" onclick="goBack()">Back to Dashboard</button>
    </div>
  </div>
  
  <div class="editor-container" id="editor-container">
    <div class="editor-pane">
      <div class="pane-header">Mermaid Code</div>
      <div id="monaco-editor"></div>
    </div>
    
    <div class="resizer" id="resizer" title="Drag to resize panes"></div>
    
    <div class="preview-pane">
      <div class="pane-header">Live Preview</div>
      <div id="mermaid-preview" class="loading">Loading preview...</div>
    </div>
  </div>
  
  <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.44.0/min/vs/loader.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
  <script src="auth.js"></script>
  <script src="diagram-editor.js"></script>
</body>
</html>
