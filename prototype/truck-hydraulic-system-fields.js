// Component attributes for the 700CPK configurator
export const componentAttributes = {
    Application: {
        name: "Application",
        dataType: "String",
        caption: "Truck & System Specifications",
        isRequired: true,
        isVisible: true,
        displayType: "TypeableDropDown",
        optionListId: "Application",
        options: [
            { value: "AG", label: "Agricultural" },
            { value: "COM", label: "Commercial" },
            { value: "OTHER", label: "Other" },
            { value: "WRK", label: "Work" }
        ]
    },
    AGKitLessMiniPack: {
        name: "AGKitLessMiniPack",
        dataType: "Boolean",
        caption: "Less AG Mini Pack?",
        isRequired: true,
        isVisible: true,
        displayType: "CheckBox",
        condition: "Application === 'AG'"
    },
    Make: {
        name: "Make",
        dataType: "String",
        caption: "Select Make of Vehicle",
        isRequired: true,
        isVisible: true,
        displayType: "TypeableDropDown",
        optionListId: "Make",
        options: [
            { value: "FORD", label: "Ford" },
            { value: "GM", label: "GM" },
            { value: "MC", label: "<PERSON>" },
            { value: "SPRINTER", label: "Sprinter" },
            { value: "OTHER", label: "Other" }
        ]
    },
    Macfit: {
        name: "Macfit",
        dataType: "Boolean",
        caption: "Click info link select yes if kit will fit:",
        isRequired: true,
        isVisible: true,
        displayType: "RadioButtonHorizontal",
        infoLink: "/bulletin/IB7042201Mack.pdf",
        condition: "Make === 'MC'",
        critical: true,
        errorMessage: "Will Not Fit"
    },
    Year: {
        name: "Year",
        dataType: "String",
        caption: "Enter 4 Digit Truck Year",
        isRequired: true,
        isVisible: true,
        displayType: "TypeableDropDown",
        optionListId: "Years",
        filter: "Make"
    },
    Vehicle_Type: {
        name: "Vehicle_Type",
        dataType: "String",
        caption: "Select Vehicle Type",
        isRequired: true,
        isVisible: true,
        displayType: "TypeableDropDown",
        optionListId: "Vehicle_Type",
        matrix: "VT_Filter",
        matrixInputs: ["Make", "Year"]
    },
    Engine: {
        name: "Engine",
        dataType: "String",
        caption: "Select Engine",
        isRequired: true,
        isVisible: true,
        displayType: "TypeableDropDown",
        optionListId: "Engine",
        matrix: "MultipleEngineGroups",
        matrixInputs: ["Make", "Vehicle_Type", "Year"],
        errorMessage: "No Engine Kit Available"
    },
    Pump: {
        name: "Pump",
        dataType: "String",
        caption: "Select Pump Size",
        isRequired: true,
        isVisible: true,
        displayType: "TypeableDropDown",
        optionListId: "Pump_1T1",
        matrix: "PumpGroupMatrix",
        matrixInputs: ["Engine", "Application", "Vehicle_Type", "Year"]
    },
    Kit: {
        name: "Kit",
        dataType: "String",
        caption: "Select Your Kit",
        isRequired: true,
        isVisible: true,
        displayType: "TypeableDropDown",
        optionListId: "Kit",
        matrix: "Kit_Group",
        matrixInputs: ["Engine", "Year", "Pump", "Vehicle_Type", "Application"],
        errorMessage: "No kit exists. Please contact Harper."
    },
    Clutch: {
        name: "Clutch",
        dataType: "String",
        caption: "Select Your Clutch",
        isRequired: true,
        isVisible: true,
        displayType: "TypeableDropDown",
        optionListId: "Clutch",
        matrix: "Clutch_Group"
    },
    Adapter: {
        name: "Adapter",
        dataType: "String",
        caption: "Select Adapter",
        isRequired: true,
        isVisible: true,
        displayType: "TypeableDropDown",
        optionListId: "Adapter",
        matrix: "MultipleAdapterGroups",
        matrixInputs: ["Kit", "Pump", "AGFlag"]
    }
};

// Matrix definitions for component filtering
export const matrices = {
    VT_Filter: {
        name: "VT_Filter",
        inputs: ["Make", "Year"],
        specialLogic: "Ford/GM/Sprinter with non-AG applications default to '3' if 'ZZ'"
    },
    MultipleEngineGroups: {
        name: "MultipleEngineGroups",
        inputs: ["Make", "Vehicle_Type", "Year"],
        outputFormat: "comma-separated-codes"
    },
    PumpGroupMatrix: {
        name: "PumpGroupMatrix",
        inputs: ["Engine", "Application", "Vehicle_Type", "Year"],
        outputFormat: "comma-separated-codes"
    },
    Kit_Group: {
        name: "Kit_Group",
        inputs: ["Engine", "Year", "Pump", "Vehicle_Type", "Application"],
        outputFormat: "dash-separated-codes"
    },
    Clutch_Group: {
        name: "Clutch_Group",
        inputs: ["current-selections"],
        override: "HD_Clutch"
    },
    MultipleAdapterGroups: {
        name: "MultipleAdapterGroups",
        inputs: ["Kit", "Pump", "AGFlag"],
        outputFormat: "comma-separated-codes"
    }
};

// Pricing matrices
export const pricingMatrices = {
    Pricing_Kit: {
        name: "Pricing_Kit",
        input: "Kit",
        output: "base-price"
    },
    Pricing_Pump: {
        name: "Pricing_Pump",
        input: "Pump",
        output: "base-price"
    },
    Pricing_Clutch: {
        name: "Pricing_Clutch",
        input: "Clutch",
        output: "base-price"
    },
    Pricing_Adapter: {
        name: "Pricing_Adapter",
        input: "Adapter",
        output: "base-price"
    }
};
