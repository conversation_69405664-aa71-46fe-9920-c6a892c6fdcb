import { componentAttributes, matrices, pricingMatrices } from './truck-hydraulic-system-fields.js';
import { calculatePrice } from './pricing.js';

const app = document.getElementById('configurator');
let state = {};
let currentStep = 0;

// Helper function to create a select element
function createSelect(attribute) {
    const options = attribute.options?.map(opt =>
        `<option value="${opt.value}">${opt.label}</option>`
    ).join('') || '';

    return `
        <label for="${attribute.name}">${attribute.caption}</label>
        <select id="${attribute.name}" required>
            <option value="">Select...</option>
            ${options}
        </select>
    `;
}

// Helper function to create a checkbox
function createCheckbox(attribute) {
    return `
        <label for="${attribute.name}">
            <input type="checkbox" id="${attribute.name}" />
            ${attribute.caption}
        </label>
    `;
}

// Helper function to create radio buttons
function createRadio(attribute) {
    return `
        <fieldset>
            <legend>${attribute.caption}</legend>
            <div class="radio-group">
                <label>
                    <input type="radio" name="${attribute.name}" value="true" />
                    Yes
                </label>
                <label>
                    <input type="radio" name="${attribute.name}" value="false" />
                    No
                </label>
            </div>
            ${attribute.infoLink ? `<a href="${attribute.infoLink}" target="_blank">More Info</a>` : ''}
        </fieldset>
    `;
}

// Define steps based on configuration flow
const steps = [
    {
        key: 'application',
        render: () => `
            ${createSelect(componentAttributes.Application)}
            <button id="nextBtn" disabled>Next</button>
        `,
        bind: (el, state, next) => {
            const select = el.querySelector('#Application');
            const btn = el.querySelector('#nextBtn');
            select.addEventListener('change', () => {
                btn.disabled = !select.value;
            });
            btn.addEventListener('click', () => {
                next({ application: select.value });
            });
        }
    },
    {
        key: 'agConfig',
        render: (state) => {
            if (state.application !== 'AG') return null;
            return `
                ${createCheckbox(componentAttributes.AGKitLessMiniPack)}
                <button id="nextBtn">Next</button>
                <button id="prevBtn">Back</button>
            `;
        },
        bind: (el, state, next, prev) => {
            if (!el) return next({}); // Skip if not AG
            const checkbox = el.querySelector('#AGKitLessMiniPack');
            const nextBtn = el.querySelector('#nextBtn');
            const prevBtn = el.querySelector('#prevBtn');
            nextBtn.addEventListener('click', () => {
                next({ AGKitLessMiniPack: checkbox.checked });
            });
            prevBtn.addEventListener('click', prev);
        }
    },
    // Add more steps here following the pattern
];

function renderStep() {
    const step = steps[currentStep];
    if (!step) {
        renderSummary();
        return;
    }

    app.innerHTML = '';
    const stepEl = document.createElement('div');
    stepEl.className = 'config-step';
    const content = step.render(state);

    if (content === null) {
        currentStep++;
        renderStep();
        return;
    }

    stepEl.innerHTML = content;
    app.appendChild(stepEl);
    step.bind(stepEl, state, nextStep, prevStep);
}

function nextStep(newState = {}) {
    state = { ...state, ...newState };
    currentStep++;
    renderStep();
}

function prevStep() {
    if (currentStep > 0) {
        currentStep--;
        renderStep();
    }
}

function renderSummary() {
    const pricing = calculatePrice(state);
    app.innerHTML = `
        <div class="summary">
            <h2>Configuration Summary</h2>
            <pre>${JSON.stringify(state, null, 2)}</pre>
            ${pricing ? `<div class="pricing">Total Price: $${pricing.toFixed(2)}</div>` : ''}
            <button onclick="location.reload()">Start Over</button>
        </div>
    `;
}

// Initialize the configurator
renderStep();
