import { componentAttributes, getVisibleFields } from './butler-fields.js';

// Initialize state
let formState = {};

function createInput(attr) {
  if (!attr.isVisible) return '';
  const required = attr.isRequired ? 'required' : '';
  const label = attr.caption || attr.name;
  let input = '';

  switch (attr.displayType) {
    case 'TypeableDropDown':
      const options = attr.options?.map(opt =>
        `<option value="${opt.value}">${opt.label}</option>`
      ).join('') || '';

      input = `
        <select id="${attr.name}" name="${attr.name}" ${required}>
          <option value="">Select ${label}</option>
          ${options}
        </select>
      `;
      break;

    case 'Number':
      const validation = attr.validation || {};
      input = `
        <input type="number" 
          id="${attr.name}" 
          name="${attr.name}"
          ${validation.min !== undefined ? `min="${validation.min}"` : ''}
          ${validation.max !== undefined ? `max="${validation.max}"` : ''}
          ${validation.step !== undefined ? `step="${validation.step}"` : ''}
          ${required}
        >
      `;
      break;

    default:
      console.warn(`Unsupported display type: ${attr.displayType}`);
      return '';
  }

  return `
    <div class="form-group">
      <label for="${attr.name}">${label}</label>
      ${input}
      ${attr.errorMessage ? `<div class="error-message">${attr.errorMessage}</div>` : ''}
    </div>
  `;
}



function renderForm(formData = {}) {
  const container = document.getElementById('configurator');
  if (!container) return;

  container.innerHTML = '';
  const visibleFields = getVisibleFields(formData);

  // Create the form element
  const form = document.createElement('form');
  form.id = 'config-form';
  container.appendChild(form);

  // Add visible fields to the form
  visibleFields.forEach(fieldName => {
    const field = componentAttributes[fieldName];
    if (field) {
      form.innerHTML += createInput(field);
    }
  });

  // Add submit button if we have fields
  if (visibleFields.length > 0) {
    form.innerHTML += '<button type="submit">Next</button>';
  }
}

// Update form state based on form element values
function updateFormState(form) {
  const newState = {};
  form.querySelectorAll('[name]').forEach(input => {
    if (input.type === 'number') {
      newState[input.name] = parseFloat(input.value) || '';
    } else {
      newState[input.name] = input.value;
    }
  });
  return newState;
}

// Update form on input
function handleInput(e) {
  const form = e.target.closest('#config-form');
  if (!form) return;

  formState = updateFormState(form);
  renderForm(formState);
}

// Initialize form when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Initial render with empty state
  renderForm(formState);

  // Attach form submit handler
  document.addEventListener('submit', (e) => {
    if (e.target.id === 'config-form') {
      e.preventDefault();
      const form = e.target;
      formState = updateFormState(form);
      console.log('Form state updated:', formState);
      renderForm(formState);
    }
  });

  // Attach input handler for live updates
  document.addEventListener('input', (e) => {
    if (e.target.closest('#config-form')) {
      handleInput(e);
    }
  });
});
